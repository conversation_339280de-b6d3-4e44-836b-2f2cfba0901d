<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>机构招募列表-青创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- swiper css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/swiper/swiper.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202505271645" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
    <style>
        .recruitment-list-container {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .page-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .page-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .page-subtitle {
            color: #666;
            font-size: 16px;
        }
        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .search-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .form-group input, .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .search-btn {
            padding: 8px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .search-btn:hover {
            background: #0056b3;
        }
        .recruitment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .recruitment-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            background: #fff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .recruitment-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .card-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .recruitment-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        .recruitment-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .meta-tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .category-tag {
            background: #e7f5e7;
            color: #28a745;
        }
        .level-tag {
            background: #fff3cd;
            color: #856404;
        }
        .status-tag {
            background: #d1ecf1;
            color: #0c5460;
        }
        .urgent-tag {
            background: #f8d7da;
            color: #721c24;
        }
        .recruitment-info {
            margin-bottom: 15px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .info-label {
            color: #666;
            font-weight: bold;
        }
        .info-value {
            color: #333;
        }
        .budget-amount {
            color: #ff6b35;
            font-weight: bold;
        }
        .recruitment-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .view-count {
            color: #999;
            font-size: 12px;
        }
        .apply-btn {
            padding: 6px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .apply-btn:hover {
            background: #1e7e34;
        }
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        .no-data-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .breadcrumb-nav {
            margin-bottom: 20px;
            color: #666;
        }
        .breadcrumb-nav a {
            color: #007bff;
            text-decoration: none;
        }
        .breadcrumb-nav a:hover {
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            .recruitment-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 底部信息条样式 */
        .bottomBar {
            width: 100%;
            background: linear-gradient(135deg, #0052d9 0%, #097fcc 50%, #13aebe 100%);
            margin-top: 0px;
        }

        .bottomContent {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .bottomLeft {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .bottomLogo {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .logoText {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            line-height: 1;
        }

        .logoSubtext {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 2px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bottomInfo {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .bottomInfo .info-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            line-height: 1.4;
        }

        .bottomRight {
            display: flex;
            gap: 60px;
        }

        .contact-info, .service-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .contact-title, .service-title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            margin: 0 0 8px 0;
        }

        .contact-item, .service-item {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .bottomContent {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .bottomLeft {
                flex-direction: column;
                gap: 20px;
            }

            .bottomRight {
                gap: 30px;
            }
        }
    </style>
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>

    <!-- main start -->
    <div class="pageBg">
        <div class="conAuto2">
            <div class="recruitment-list-container">
                <div class="page-header">
                    <h1 class="page-title">机构招募列表</h1>
                    <p class="page-subtitle">发现优质培训机构合作机会，共同提升培训质量</p>
                </div>

                <!-- 搜索区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label>关键词</label>
                            <input type="text" id="searchKeyword" placeholder="请输入招募标题关键词">
                        </div>
                        <div class="form-group">
                            <label>培训类别</label>
                            <select id="searchCategory">
                                <option value="">全部类别</option>
                                <option value="IT技能">IT技能</option>
                                <option value="管理培训">管理培训</option>
                                <option value="职业技能">职业技能</option>
                                <option value="创业指导">创业指导</option>
                                <option value="财务管理">财务管理</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>培训级别</label>
                            <select id="searchLevel">
                                <option value="">全部级别</option>
                                <option value="初级">初级</option>
                                <option value="中级">中级</option>
                                <option value="高级">高级</option>
                            </select>
                        </div>
                        <button class="search-btn" onclick="searchRecruitments()">搜索</button>
                    </div>
                </div>

                <!-- 招募列表 -->
                <div class="recruitment-grid" id="recruitmentGrid">
                    <!-- 招募卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 无数据提示 -->
                <div class="no-data" id="noDataTip" style="display: none;">
                    <div class="no-data-icon">🏢</div>
                    <p>暂无机构招募信息</p>
                    <p style="color: #ccc; font-size: 14px;">精彩招募项目即将发布，敬请期待</p>
                </div>

                <!-- 分页 -->
                <div class="pagination-container">
                    <div id="pagination"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- main end -->

    <!-- 底部信息条 -->
    <div class="bottomBar">
        <div class="conAuto1400">
            <div class="bottomContent">
                <div class="bottomLeft">
                    <div class="bottomLogo">
                        <span class="logoText">就业培训平台</span>
                        <span class="logoSubtext">Employment Training Platform</span>
                    </div>
                    <div class="bottomInfo">
                        <p class="info-text">致力于为求职者提供专业的就业培训服务</p>
                        <p class="info-text">打造高质量的职业技能提升平台</p>
                    </div>
                </div>
                <div class="bottomRight">
                    <div class="contact-info">
                        <p class="contact-title">联系我们</p>
                        <p class="contact-item">服务热线：400-123-4567</p>
                        <p class="contact-item">邮箱：<EMAIL></p>
                    </div>
                    <div class="service-info">
                        <p class="service-title">服务时间</p>
                        <p class="service-item">周一至周五 9:00-18:00</p>
                        <p class="service-item">周六至周日 9:00-17:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/institutionRecruitmentList.js?v=202507251200" type="text/javascript" charset="utf-8"></script>
</body>

</html>
