// 公用模块html
headerBar()
footerBar()

// 机构招募数据存储
var recruitmentData = [];
var currentPage = 1;
var pageSize = 12;
var totalCount = 0;

// 跳转到机构招募详情页
function goRecruitmentDetail(recruitmentId) {
    window.open('institutionRecruitmentDetail.html?id=' + recruitmentId);
}

// 机构招募原生Ajax请求函数
function recruitmentAjaxRequest(url, params, callback) {
    var baseUrl = 'http://************/sux-admin/';
    // var baseUrl = 'http://localhost:80/sux-admin/';

    // 构建查询参数
    var queryString = '';
    if (params && typeof params === 'object') {
        var paramArray = [];
        for (var key in params) {
            if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
            }
        }
        queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
    }

    var xhr = new XMLHttpRequest();
    xhr.open('GET', baseUrl + url + queryString, true);
    xhr.timeout = 30000;
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '解析响应数据失败',
                            rows: [],
                            total: 0
                        });
                    }
                }
            } else {
                console.error('机构招募请求失败:', xhr.status, xhr.statusText);
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                        rows: [],
                        total: 0
                    });
                }
            }
        }
    };

    xhr.ontimeout = function() {
        console.error('机构招募请求超时');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '请求超时',
                rows: [],
                total: 0
            });
        }
    };

    xhr.onerror = function() {
        console.error('机构招募请求发生错误');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '网络错误',
                rows: [],
                total: 0
            });
        }
    };

    xhr.send();
}

// 获取机构招募列表
function loadRecruitmentList(page) {
    page = page || 1;
    currentPage = page;

    var params = {
        pageNum: page,
        pageSize: pageSize,
        recruitmentStatus: '1' // 只查询已发布的招募
    };

    // 添加搜索条件
    var keyword = document.getElementById('searchKeyword').value.trim();
    var category = document.getElementById('searchCategory').value;
    var level = document.getElementById('searchLevel').value;

    if (keyword) {
        params.recruitmentTitle = keyword;
    }
    if (category) {
        params.trainingCategory = category;
    }
    if (level) {
        params.trainingLevel = level;
    }

    recruitmentAjaxRequest('public/recruitment/institution/list', params, function(data) {
        if (data.code == 0 || data.code == 200) {
            recruitmentData = data.rows || [];
            totalCount = data.total || 0;
            renderRecruitmentList();
            renderPagination();
        } else {
            console.error('获取机构招募列表失败：', data.msg);
            // 如果API失败，加载模拟数据用于测试
            loadMockRecruitmentList();
        }
    });
}

// 渲染机构招募列表
function renderRecruitmentList() {
    var container = document.getElementById('recruitmentGrid');
    var noDataTip = document.getElementById('noDataTip');

    if (!recruitmentData || recruitmentData.length === 0) {
        container.innerHTML = '';
        noDataTip.style.display = 'block';
        return;
    }

    noDataTip.style.display = 'none';
    var html = '';

    recruitmentData.forEach(function(item) {
        var statusText = getStatusText(item.recruitmentStatus);
        var statusClass = getStatusClass(item.recruitmentStatus);
        var budgetText = item.budgetAmount && item.budgetAmount > 0 ? 
            '￥' + parseFloat(item.budgetAmount).toFixed(2) : '面议';
        var isUrgent = item.isUrgent === '1';
        var description = item.recruitmentDescription || '暂无描述';
        
        // 截取描述文本
        if (description.length > 100) {
            description = description.substring(0, 100) + '...';
        }

        html += `
            <div class="recruitment-card" onclick="goRecruitmentDetail(${item.recruitmentId})">
                <div class="card-header">
                    <div>
                        <h3 class="recruitment-title">${item.recruitmentTitle || '--'}</h3>
                        <div class="recruitment-meta">
                            <span class="meta-tag category-tag">${item.trainingCategory || '--'}</span>
                            <span class="meta-tag level-tag">${item.trainingLevel || '--'}</span>
                            <span class="meta-tag status-tag ${statusClass}">${statusText}</span>
                            ${isUrgent ? '<span class="meta-tag urgent-tag">紧急</span>' : ''}
                        </div>
                    </div>
                </div>
                
                <div class="recruitment-info">
                    <div class="info-row">
                        <span class="info-label">培训时长：</span>
                        <span class="info-value">${item.trainingDuration ? item.trainingDuration + '小时' : '--'}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">预算金额：</span>
                        <span class="info-value budget-amount">${budgetText}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">需求人数：</span>
                        <span class="info-value">${item.requiredInstructors || '--'}人</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">申请截止：</span>
                        <span class="info-value">${formatDate(item.applicationDeadline) || '--'}</span>
                    </div>
                </div>
                
                <div class="recruitment-description">${description}</div>
                
                <div class="card-footer">
                    <span class="view-count">浏览 ${item.viewCount || 0} 次</span>
                    <button class="apply-btn" onclick="event.stopPropagation(); applyRecruitment(${item.recruitmentId})">
                        立即申请
                    </button>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 获取状态文本
function getStatusText(status) {
    var statusMap = {
        '0': '草稿',
        '1': '已发布',
        '2': '进行中',
        '3': '已完成',
        '4': '已取消'
    };
    return statusMap[status] || '未知';
}

// 获取状态样式类
function getStatusClass(status) {
    var classMap = {
        '0': 'status-draft',
        '1': 'status-published',
        '2': 'status-ongoing',
        '3': 'status-completed',
        '4': 'status-cancelled'
    };
    return classMap[status] || 'status-unknown';
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '--';
    var date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    
    var year = date.getFullYear();
    var month = String(date.getMonth() + 1).padStart(2, '0');
    var day = String(date.getDate()).padStart(2, '0');
    
    return year + '-' + month + '-' + day;
}

// 申请机构招募
function applyRecruitment(recruitmentId) {
    // 跳转到后台申请页面
    var backendUrl = 'http://************/admin/';
    // var backendUrl = 'http://localhost:81/';
    var applyUrl = backendUrl + '#/ret/applicationititution?forceLogout=true&recruitmentId=' + recruitmentId;
    window.open(applyUrl, '_blank');
}

// 搜索机构招募
function searchRecruitments() {
    loadRecruitmentList(1);
}

// 渲染分页
function renderPagination() {
    if (totalCount <= pageSize) {
        document.getElementById('pagination').innerHTML = '';
        return;
    }

    var totalPages = Math.ceil(totalCount / pageSize);
    var paginationHtml = '';

    // 上一页
    if (currentPage > 1) {
        paginationHtml += `<a href="javascript:;" onclick="loadRecruitmentList(${currentPage - 1})" class="page-link">上一页</a>`;
    }

    // 页码
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPages, currentPage + 2);

    for (var i = startPage; i <= endPage; i++) {
        var activeClass = i === currentPage ? 'active' : '';
        paginationHtml += `<a href="javascript:;" onclick="loadRecruitmentList(${i})" class="page-link ${activeClass}">${i}</a>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `<a href="javascript:;" onclick="loadRecruitmentList(${currentPage + 1})" class="page-link">下一页</a>`;
    }

    document.getElementById('pagination').innerHTML = paginationHtml;
}

// 加载模拟数据（用于测试）
function loadMockRecruitmentList() {
    recruitmentData = [
        {
            recruitmentId: 1,
            recruitmentTitle: 'Java高级开发培训师招募',
            recruitmentDescription: '我们正在寻找具有丰富Java开发经验的培训师，负责企业内部Java高级开发培训课程的设计和实施。要求具备5年以上Java开发经验，熟悉Spring Boot、微服务架构等技术栈。',
            trainingCategory: 'IT技能',
            trainingLevel: '高级',
            trainingDuration: 120,
            budgetAmount: 50000.00,
            requiredInstructors: 2,
            applicationDeadline: '2025-08-15',
            recruitmentStatus: '1',
            isUrgent: '1',
            viewCount: 156
        },
        {
            recruitmentId: 2,
            recruitmentTitle: '企业管理培训机构合作',
            recruitmentDescription: '寻找专业的企业管理培训机构，为我们的中高层管理人员提供系统的管理培训课程。包括领导力、团队管理、战略规划等方面的培训内容。',
            trainingCategory: '管理培训',
            trainingLevel: '中级',
            trainingDuration: 80,
            budgetAmount: 30000.00,
            requiredInstructors: 3,
            applicationDeadline: '2025-08-20',
            recruitmentStatus: '1',
            isUrgent: '0',
            viewCount: 89
        }
    ];
    totalCount = recruitmentData.length;
    renderRecruitmentList();
    renderPagination();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadRecruitmentList(1);
});

// 回车搜索
document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchRecruitments();
    }
});
