<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>机构招募详情-青创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/activityDetail.css?v=202502281010" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
    <style>
        .recruitment-detail {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .detail-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .recruitment-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .recruitment-meta {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .recruitment-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }

        .status-published {
            background: #e7f5e7;
            color: #28a745;
        }

        .status-ongoing {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .budget-amount {
            font-size: 24px;
            color: #ff6b35;
            font-weight: bold;
        }

        .urgent-badge {
            background: #ff4d4f;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .detail-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .detail-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .detail-section h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
            font-size: 18px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
        }

        .info-item label {
            font-weight: bold;
            color: #666;
            min-width: 120px;
        }

        .description-content,
        .requirements-content,
        .criteria-content {
            line-height: 1.6;
            color: #555;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .stats-panel {
            position: sticky;
            top: 20px;
            background: #fff;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-panel h4 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
            font-weight: bold;
        }

        .action-panel {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: sticky;
            top: 20px;
        }

        .requirements-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .requirement-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #007bff;
        }

        .requirement-item h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            font-weight: bold;
        }

        .requirement-content {
            color: #555;
            line-height: 1.6;
            font-size: 14px;
        }

        .btn {
            display: block;
            width: 100%;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn-apply {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 16px 20px;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            max-width: 100%;
            box-sizing: border-box;
        }

        .btn-apply:hover {
            background: linear-gradient(135deg, #218838, #1e7e34);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .btn-apply:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
        }

        .btn-apply::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-apply:hover::before {
            left: 100%;
        }

        .btn-icon {
            font-size: 18px;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
        }

        .btn-text {
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 0.5px;
        }

        .related-recruitments {
            background: #fff;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
        }

        .related-recruitments h4 {
            margin-bottom: 15px;
            color: #333;
        }

        .breadcrumb-nav {
            margin-bottom: 20px;
            color: #666;
        }

        .breadcrumb-nav a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumb-nav a:hover {
            text-decoration: underline;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .detail-content {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .btn-apply {
                padding: 16px 20px;
                font-size: 16px;
            }

            .btn-icon {
                font-size: 18px;
            }

            .btn-text {
                font-size: 16px;
            }

            .action-panel {
                position: static;
                margin-top: 20px;
            }

            .stats-panel {
                position: static;
            }
        }

        /* 底部信息条样式 */
        .bottomBar {
            width: 100%;
            background: linear-gradient(135deg, #0052d9 0%, #097fcc 50%, #13aebe 100%);
            margin-top: 40px;
            padding: 20px 0;
        }

        .bottomContent {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .bottomLeft {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .bottomLogo {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .logoText {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            line-height: 1;
        }

        .logoSubtext {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 2px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bottomInfo {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .bottomInfo .info-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            line-height: 1.4;
        }

        .bottomRight {
            display: flex;
            gap: 60px;
        }

        .contact-info, .service-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .contact-title, .service-title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            margin: 0 0 8px 0;
        }

        .contact-item, .service-item {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .bottomContent {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .bottomLeft {
                flex-direction: column;
                gap: 20px;
            }

            .bottomRight {
                gap: 30px;
            }
        }
    </style>
</head>

<body>
    <div id="headerBar"></div>

    <!-- main start -->
    <div class="pageBg">
        <div class="conAuto2">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-nav">
                <a href="index.html">首页</a> >
                <a href="institutionRecruitmentList.html">机构招募</a> >
                <span>招募详情</span>
            </div>

            <div class="recruitment-detail">
                <div class="detail-header">
                    <h1 class="recruitment-title" id="recruitmentTitle">加载中...</h1>
                    <div class="recruitment-meta">
                        <span class="recruitment-status" id="recruitmentStatus">--</span>
                        <span class="budget-amount" id="budgetAmount">--</span>
                        <span class="urgent-badge" id="urgentBadge" style="display: none;">紧急</span>
                    </div>
                </div>

                <div class="detail-content">
                    <div class="content-left">
                        <!-- 招募信息 -->
                        <div class="detail-section">
                            <h3>招募信息</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>培训类别：</label>
                                    <span id="trainingCategory">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训级别：</label>
                                    <span id="trainingLevel">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训时长：</label>
                                    <span id="trainingDuration">--</span>
                                </div>
                                <div class="info-item">
                                    <label>最大参与人数：</label>
                                    <span id="maxParticipants">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训地点：</label>
                                    <span id="trainingLocation">--</span>
                                </div>
                                <div class="info-item">
                                    <label>付款方式：</label>
                                    <span id="paymentMethod">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训费用范围：</label>
                                    <span id="trainingFeeRange">--</span>
                                </div>
                                <div class="info-item">
                                    <label>支持方式：</label>
                                    <span id="supportMethods">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>时间安排</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>申请开始时间：</label>
                                    <span id="applicationStartDate">--</span>
                                </div>
                                <div class="info-item">
                                    <label>申请截止时间：</label>
                                    <span id="applicationEndDate">--</span>
                                </div>
                                <div class="info-item">
                                    <label>预期开始时间：</label>
                                    <span id="expectedStartDate">--</span>
                                </div>
                                <div class="info-item">
                                    <label>预期结束时间：</label>
                                    <span id="expectedEndDate">--</span>
                                </div>
                                <div class="info-item">
                                    <label>创建时间：</label>
                                    <span id="createTime">--</span>
                                </div>
                                <div class="info-item">
                                    <label>更新时间：</label>
                                    <span id="updateTime">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>招募描述</h3>
                            <div class="description-content" id="recruitmentDescription">
                                暂无描述
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>机构要求</h3>
                            <div class="requirements-list">
                                <div class="requirement-item">
                                    <h4>资质要求</h4>
                                    <div class="requirement-content" id="qualificationRequirements">--</div>
                                </div>
                                <div class="requirement-item">
                                    <h4>经验要求</h4>
                                    <div class="requirement-content" id="experienceRequirements">--</div>
                                </div>
                                <div class="requirement-item">
                                    <h4>师资要求</h4>
                                    <div class="requirement-content" id="teacherRequirements">--</div>
                                </div>
                                <div class="requirement-item">
                                    <h4>设施要求</h4>
                                    <div class="requirement-content" id="facilityRequirements">--</div>
                                </div>
                                <div class="requirement-item">
                                    <h4>附加要求</h4>
                                    <div class="requirement-content" id="additionalRequirements">--</div>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>评选标准</h3>
                            <div class="criteria-content" id="evaluationCriteria">
                                待确定
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>联系方式</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>联系人：</label>
                                    <span id="contactPerson">--</span>
                                </div>
                                <div class="info-item">
                                    <label>联系电话：</label>
                                    <span id="contactPhone">--</span>
                                </div>
                                <div class="info-item">
                                    <label>联系邮箱：</label>
                                    <span id="contactEmail">--</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="content-right">
                        <!-- 统计信息面板 -->
                        <div class="stats-panel">
                            <h4>招募统计</h4>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number" id="viewCount">--</div>
                                    <div class="stat-label">浏览次数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="applicationCount">--</div>
                                    <div class="stat-label">申请次数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="selectedCount">--</div>
                                    <div class="stat-label">已选中</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="budgetDisplay">--</div>
                                    <div class="stat-label">预算金额</div>
                                </div>
                            </div>
                            <button class="btn btn-apply" id="applyBtn" onclick="handleApply()">
                                <span class="btn-icon">📝</span>
                                <span class="btn-text">立即申请</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- main end -->

    <!-- 底部信息条 -->
    <div class="bottomBar">
        <div class="conAuto1400">
            <div class="bottomContent">
                <div class="bottomLeft">
                    <div class="bottomLogo">
                        <span class="logoText">就业培训平台</span>
                        <span class="logoSubtext">Employment Training Platform</span>
                    </div>
                    <div class="bottomInfo">
                        <p class="info-text">致力于为求职者提供专业的就业培训服务</p>
                        <p class="info-text">打造高质量的职业技能提升平台</p>
                    </div>
                </div>
                <div class="bottomRight">
                    <div class="contact-info">
                        <p class="contact-title">联系我们</p>
                        <p class="contact-item">服务热线：400-123-4567</p>
                        <p class="contact-item">邮箱：<EMAIL></p>
                    </div>
                    <div class="service-info">
                        <p class="service-title">服务时间</p>
                        <p class="service-item">周一至周五 9:00-18:00</p>
                        <p class="service-item">周六至周日 9:00-17:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>

    <script>
        // 公用模块html
        headerBar()
        footerBar()

        var recruitmentDetail = {};
        var recruitmentId = getUrlParam('id') || '1';

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        // 自定义Ajax请求函数
        function customAjaxRequest(url, params, callback) {
            var baseUrl = 'http://************:80/sux-admin/';
            // var baseUrl = 'http://localhost:80/sux-admin/';

            $.ajax({
                url: baseUrl + url,
                type: 'GET',
                data: params,
                dataType: 'json',
                timeout: 30000,
                success: function (response) {
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('请求失败:', error);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '请求失败: ' + error,
                            data: null
                        });
                    }
                }
            });
        }

        // 获取招募详情
        function getRecruitmentDetail() {
            customAjaxRequest('public/recruitment/institution/' + recruitmentId, {}, function (data) {
                if (data.code == 0 || data.code == 200) {
                    var recruitmentData = data.data || data.obj || data;
                    if (recruitmentData && recruitmentData.recruitmentId) {
                        recruitmentDetail = recruitmentData;
                        renderRecruitmentDetail(recruitmentData);
                    } else {
                        alert('招募数据格式错误');
                        loadMockRecruitmentDetail();
                    }
                } else {
                    alert('获取招募详情失败：' + (data.msg || data.message || '未知错误'));
                    loadMockRecruitmentDetail();
                }
            });
        }

        // 渲染招募详情
        function renderRecruitmentDetail(data) {
            document.getElementById('recruitmentTitle').textContent = data.recruitmentTitle || '--';

            // 状态显示
            var statusText = getStatusText(data.recruitmentStatus);
            var statusElement = document.getElementById('recruitmentStatus');
            statusElement.textContent = statusText;
            statusElement.className = 'recruitment-status ' + getStatusClass(data.recruitmentStatus);

            // 预算金额
            var budgetText = data.budgetAmount && data.budgetAmount > 0 ?
                '￥' + parseFloat(data.budgetAmount).toFixed(2) : '面议';
            document.getElementById('budgetAmount').textContent = budgetText;

            // 紧急标识
            if (data.isUrgent === '1') {
                document.getElementById('urgentBadge').style.display = 'inline-block';
            }

            // 基本信息
            document.getElementById('trainingCategory').textContent = data.trainingCategory || '--';
            document.getElementById('trainingLevel').textContent = data.trainingLevel || '--';
            document.getElementById('trainingDuration').textContent = data.trainingDuration ? data.trainingDuration + '小时' : '--';
            document.getElementById('maxParticipants').textContent = data.maxParticipants ? data.maxParticipants + '人' : '--';
            document.getElementById('trainingLocation').textContent = data.trainingLocation || '--';
            document.getElementById('paymentMethod').textContent = data.paymentMethod || '--';

            // 培训费用范围
            var feeRangeText = '--';
            if (data.trainingFeeMin && data.trainingFeeMax) {
                feeRangeText = '￥' + parseFloat(data.trainingFeeMin).toFixed(2) + ' - ￥' + parseFloat(data.trainingFeeMax).toFixed(2);
            } else if (data.trainingFeeMin) {
                feeRangeText = '￥' + parseFloat(data.trainingFeeMin).toFixed(2) + ' 起';
            }
            document.getElementById('trainingFeeRange').textContent = feeRangeText;

            // 支持方式
            var supportMethods = [];
            if (data.onlineSupport === '1') supportMethods.push('线上');
            if (data.offlineSupport === '1') supportMethods.push('线下');
            document.getElementById('supportMethods').textContent = supportMethods.length > 0 ? supportMethods.join('、') : '--';

            // 时间信息
            document.getElementById('applicationStartDate').textContent = formatDate(data.applicationStartDate);
            document.getElementById('applicationEndDate').textContent = formatDate(data.applicationEndDate);
            document.getElementById('expectedStartDate').textContent = formatDate(data.expectedStartDate);
            document.getElementById('expectedEndDate').textContent = formatDate(data.expectedEndDate);
            document.getElementById('createTime').textContent = formatDate(data.createTime);
            document.getElementById('updateTime').textContent = formatDate(data.updateTime);

            // 描述信息
            document.getElementById('recruitmentDescription').innerHTML = data.recruitmentDescription || '暂无描述';
            document.getElementById('evaluationCriteria').innerHTML = data.evaluationCriteria || '待确定';

            // 机构要求
            document.getElementById('qualificationRequirements').innerHTML = data.qualificationRequirements || '无特殊要求';
            document.getElementById('experienceRequirements').innerHTML = data.experienceRequirements || '无特殊要求';
            document.getElementById('teacherRequirements').innerHTML = data.teacherRequirements || '无特殊要求';
            document.getElementById('facilityRequirements').innerHTML = data.facilityRequirements || '无特殊要求';
            document.getElementById('additionalRequirements').innerHTML = data.additionalRequirements || '无特殊要求';

            // 联系方式
            document.getElementById('contactPerson').textContent = data.contactPerson || '--';
            document.getElementById('contactPhone').textContent = data.contactPhone || '--';
            document.getElementById('contactEmail').textContent = data.contactEmail || '--';

            // 统计信息
            document.getElementById('viewCount').textContent = data.viewCount || 0;
            document.getElementById('applicationCount').textContent = data.applicationCount || 0;
            document.getElementById('selectedCount').textContent = data.selectedCount || 0;

            // 预算显示
            var budgetDisplayText = data.budgetAmount && data.budgetAmount > 0 ?
                '￥' + parseFloat(data.budgetAmount).toFixed(2) : '面议';
            document.getElementById('budgetDisplay').textContent = budgetDisplayText;

            // 更新页面标题
            document.title = (data.recruitmentTitle || '机构招募详情') + ' - 青创通 · 西宁市创业服务云平台';
        }

        // 获取状态文本
        function getStatusText(status) {
            var statusMap = {
                '0': '草稿',
                '1': '已发布',
                '2': '进行中',
                '3': '已完成',
                '4': '已取消'
            };
            return statusMap[status] || '未知';
        }

        // 获取状态样式类
        function getStatusClass(status) {
            var classMap = {
                '0': 'status-draft',
                '1': 'status-published',
                '2': 'status-ongoing',
                '3': 'status-completed',
                '4': 'status-cancelled'
            };
            return classMap[status] || 'status-unknown';
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '--';
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr;

            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');

            return year + '-' + month + '-' + day;
        }

        // 处理申请
        function handleApply() {
            if (!recruitmentDetail.recruitmentId) {
                alert('招募信息加载中，请稍后再试');
                return;
            }

            // 检查申请条件
            if (recruitmentDetail.recruitmentStatus === '4') {
                alert('招募已取消');
                return;
            }

            if (recruitmentDetail.recruitmentStatus === '3') {
                alert('招募已完成');
                return;
            }

            if (recruitmentDetail.recruitmentStatus === '0') {
                alert('招募尚未发布');
                return;
            }

            // 跳转到后台申请页面
            var backendUrl = 'http://************/admin/';
            // var backendUrl = 'http://localhost:81/';
            var applyUrl = backendUrl + '#/ret/applicationititution?forceLogout=true&recruitmentId=' + recruitmentDetail.recruitmentId;
            window.open(applyUrl, '_blank');
        }

        // 添加到收藏
        function addToFavorites() {
            alert('收藏功能开发中...');
        }

        // 分享招募
        function shareRecruitment() {
            if (navigator.share) {
                navigator.share({
                    title: recruitmentDetail.recruitmentTitle || '机构招募',
                    text: '查看这个机构招募信息',
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(function () {
                    alert('链接已复制到剪贴板');
                }).catch(function () {
                    alert('分享链接：' + window.location.href);
                });
            }
        }

        // 返回列表
        function goBack() {
            window.location.href = 'institutionRecruitmentList.html';
        }

        // 加载模拟数据
        function loadMockRecruitmentDetail() {
            var mockData = {
                recruitmentId: recruitmentId,
                recruitmentTitle: 'Java高级开发培训师招募',
                recruitmentDescription: '我们正在寻找具有丰富Java开发经验的培训师，负责企业内部Java高级开发培训课程的设计和实施。要求具备5年以上Java开发经验，熟悉Spring Boot、微服务架构等技术栈。',
                trainingCategory: 'IT技能',
                trainingLevel: '高级',
                trainingDuration: 120,
                budgetAmount: 50000.00,
                requiredInstructors: 2,
                trainingLocation: '西宁市城西区',
                paymentMethod: '项目完成后一次性支付',
                expectedStartDate: '2025-09-01',
                expectedEndDate: '2025-12-31',
                applicationDeadline: '2025-08-15',
                publishTime: '2025-07-20',
                institutionRequirements: '1. 具备相关培训资质\n2. 拥有专业的师资团队\n3. 有成功的培训案例\n4. 能够提供完整的培训方案',
                evaluationCriteria: '1. 机构资质和信誉(30%)\n2. 师资力量和经验(40%)\n3. 培训方案的完整性和创新性(20%)\n4. 报价合理性(10%)',
                contactPerson: '张经理',
                contactPhone: '0971-1234567',
                contactEmail: '<EMAIL>',
                recruitmentStatus: '1',
                isUrgent: '1',
                viewCount: 156,
                applicationCount: 8
            };

            recruitmentDetail = mockData;
            renderRecruitmentDetail(mockData);
        }

        // 初始化
        getRecruitmentDetail();
    </script>
</body>

</html>